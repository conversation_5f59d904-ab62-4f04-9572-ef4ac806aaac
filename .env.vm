# Non-secret environment variables for LongevityCo VM deployment
API_HOST=0.0.0.0
API_PORT=8000
APP_ENV=vm
DEBUG=True
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
POSTGRES_USER=longevity
POSTGRES_DB=longevity
MINIO_SECURE=False
STORAGE_BUCKET_NAME=longevity-documents
STORAGE_PROVIDER=gcs
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
EMBEDDING_MODEL=intfloat/e5-large-v2
EMBEDDING_DIMENSION=1024
MAX_DOCUMENT_SIZE_MB=50
GCS_PROJECT_ID=rosy-rider-453708-c3
GOOGLE_APPLICATION_CREDENTIALS=gcs_login.json
VERTEX_AI_PROJECT_ID=rosy-rider-453708-c3
VERTEX_AI_LOCATION=europe-west4
VERTEX_AI_MODEL_ID=publishers/google/models/gemini-2.0-flash-001
VERTEX_AI_BATCH_MODEL_ID=projects/rosy-rider-453708-c3/locations/europe-west4/publishers/google/models/gemini-2.0-flash-001
VERTEX_AI_MODEL_NAME=google/gemini-2.0-flash-001
#test=>
GOOGLE_DRIVE_FOLDER_ID=11SzDukhfSxRI2JH-tdsltgwo2XfPcLfS
#GOOGLE_DRIVE_FOLDER_ID=1lx4KxIhL5o-2b1si7RgSmQoIkhuw026X
GDRIVE_CHECK_INTERVAL_SECONDS=300
GCS_BUCKET_NAME=longevity-batch-predictions
GCS_CREDENTIALS_FILE=gcs_login.json
NEO4J_USER=neo4j
# NEO4J_PASSWORD and POSTGRES_PASSWORD are secrets and will be injected by fetch_secrets.sh
